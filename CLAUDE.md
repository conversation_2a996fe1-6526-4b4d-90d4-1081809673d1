1. here is responce from dcustom dataRoute 
{
    "data": [
        {
            "id": "item_68af09abbe89f_1756301739",
            "Address": "yes",
            "Details": "yes",
            "Latitude": "-1.762688",
            "Longitude": "30.035097",
            "Timestamp": "2025-08-30",
            "timestamp": "2025-08-27T13:35:39.780481Z"
        }
    ],
    "currentPage": 1,
    "lastPage": 1,
    "itemsPerPage": 10,
    "pageItems": 1,
    "total": 1
}

they are always paginated , so add pagination option like perpage and page here too 

2 . here is the payload from shareMapDataRequest 
{
    "mapData": {
        "id": 7,
        "name": "RURA Cards",
        "image": "pin",
        "description": "jcvn sdjkfn kadjfn dajfn adhj ,dhfbadfh adjh ,da",
        "visibility": "public",
        "status": "active",
        "zoom": "12",
        "place_map_items_count": 0,
        "data_map": [
            {
                "id": 6,
                "name": "GPS Tracking",
                "visibility": "private",
                "status": "active",
                "place_map_id": 7,
                "data_map_items": [
                    {
                        "id": 5,
                        "visibility": "public",
                        "status": "active",
                        "place_map_item_id": 16,
                        "data_map_id": 6
                    },
                    {
                        "id": 6,
                        "visibility": "public",
                        "status": "active",
                        "place_map_item_id": 18,
                        "data_map_id": 6
                    },
                    {
                        "id": 7,
                        "visibility": "public",
                        "status": "active",
                        "place_map_item_id": 27,
                        "data_map_id": 6
                    }
                ]
            }
        ]
    },
    "placeItems": {
        "data": [
            {
                "id": 27,
                "name": "hirwa",
                "image": "pin",
                "description": "3;k4jn3qui;4ojh;3qio4uthj34uith34uth34tui3",
                "latitude": "-2.0281820",
                "longitude": "30.4229890",
                "type": "movingItem",
                "address": "East, Rwamagana, Kigabiro, Sovu, Umudugudu wa Nyabishunzi",
                "visibility": "private",
                "status": "active",
                "place_map_id": 7
            },
            {
                "id": 26,
                "name": "qwuhfluqwn jkil vuqwhviwovjqweiovjqweivjqwio",
                "image": "pin",
                "description": "2o3jrio23rjio23rjo2i3r23",
                "latitude": "-1.6130690",
                "longitude": "30.1261750",
                "type": "movingItem",
                "address": "North, Gicumbi, Rukomo, Gisiza, Umudugudu wa Karambi",
                "visibility": "private",
                "status": "active",
                "place_map_id": 7
            },


            the you made is data_map is also an array of items , like user has to choose data to see like map daheve have 2 dataMap like one for gps tracking and one to see drivers for all those tracks so users has to switch to whihc they have to se like i have to select data map only if we have one data map where user dont have to select 


            3 . limit the map to be on rwanda lat and long when zoomed out all in , it dont have to be far that 
            4 . give the user the best expreince