<script setup>
import { ref, onMounted, onUnmounted, defineComponent, h, watch } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import axios from 'axios';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { debounce } from 'lodash';

const props = defineProps({
    mapKey: {
        type: String,
        required: true,
    },
});

// --- STATE REFS ---
const mapContainer = ref(null);
const map = ref(null);
const mapData = ref(null);
const placeItems = ref(null);
const isLoading = ref(true);
const error = ref(null);
const selectedTheme = ref(localStorage.getItem('mapTheme') || 'Light');

// Search and pagination state
const searchQuery = ref('');
const currentPage = ref(1);
const itemsPerPage = ref(10);
const isSearching = ref(false);
const showSidebar = ref(true);

// Custom data side navigation state
const showCustomDataSidebar = ref(false);
const selectedPlaceItem = ref(null);
const customDataItems = ref([]);
const customDataPagination = ref(null);
const customDataLoading = ref(false);
const customDataError = ref(null);
const customDataCurrentPage = ref(1);
const customDataItemsPerPage = ref(10);

// Per page options
const perPageOptions = [5, 10, 15, 20, 25, 50];

// Debounced search function
const debouncedSearch = debounce(async (query) => {
    currentPage.value = 1;
    await fetchMapData(1, query);
}, 300);



// --- CONFIGURATION ---
const MAP_CONFIG = {
    center: [29.8739, -1.9403], // Default center for Rwanda
    zoom: 8,
    fitBoundsPadding: { top: 100, bottom: 100, left: 100, right: 100 },
};



const UI_CONFIG = {
    mapThemes: {
        'Light': {
            url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            paint: { 'raster-saturation': -0.2, 'raster-contrast': 0.1, 'raster-opacity': 0.9 }
        },
        'Satellite': {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            paint: {}
        }
    },
};

// --- DYNAMIC ICON COMPONENT ---
const SvgIcon = defineComponent({
    props: ['name', 'size'],
    setup(props) {
        const size = props.size || 24;
        const iconPaths = {
            pin: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/><circle cx="12" cy="9" r="2.5"/>`,
            star: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
            heart: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
            flag: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
            home: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
            work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
            cafe: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="currentColor" stroke-width="2" fill="none"/>`,
            park: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22v-6m0 0l-3-3m3 3l3-3m-3-10v10m0-10l-3-3m3 3l3-3M12 2v10"/><path d="M5 12h14" stroke="currentColor" stroke-width="2"/>`,
            restaurant: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
            shopping: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
            hospital: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"/><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            school: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,

        };
        return () => h('svg', {
            width: size,
            height: size,
            viewBox: '0 0 24 24',
            fill: 'none',
            stroke: 'currentColor',
            innerHTML: iconPaths[props.name] || iconPaths.pin
        });
    }
});



// --- EXISTING LOGIC FUNCTIONS ---
const fetchMapData = async (page = 1, search = '') => {
    if (page === 1) {
        isLoading.value = true;
    } else {
        isSearching.value = true;
    }
    error.value = null;

    try {
        const params = new URLSearchParams({
            page: page.toString(),
            perPage: itemsPerPage.value.toString(),
        });

        if (search.trim()) {
            params.append('searchQuery', search.trim());
        }

        const response = await axios.get(`/map/shared/map-data/${props.mapKey}?${params}`);

        // Update state with new API structure
        mapData.value = response.data.mapData;
        placeItems.value = response.data.placeItems;
        currentPage.value = response.data.placeItems.currentPage;

        setupMapMarkers();
    } catch (err) {
        console.error('Error fetching map data:', err);
        error.value = err.response?.data?.message || 'Could not load the map data. The link may be invalid or expired.';
    } finally {
        isLoading.value = false;
        isSearching.value = false;
    }
};

const setupMapMarkers = () => {
    if (!map.value || !placeItems.value) return;

    // Clear existing markers
    const existingMarkers = document.querySelectorAll('.custom-marker');
    existingMarkers.forEach(marker => marker.remove());

    const bounds = new maplibregl.LngLatBounds();
    placeItems.value.data.forEach(item => {
        if (item.latitude && item.longitude) {
            const el = document.createElement('div');
            el.className = 'custom-marker';
            
            // Check if item has a custom image URL or use icon
            const iconContent = item.image && item.image.startsWith('http') 
                ? `<img src="${item.image}" alt="${item.name}" class="marker-custom-image" />`
                : `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                     <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/>
                     <circle cx="12" cy="9" r="2.5"/>
                     ${getIconPath(item.image || 'pin')}
                   </svg>`;
                   
            el.innerHTML = `<div class="marker-icon-container">${iconContent}</div>`;

            const popupContent = `
                <div class="popup-card">
                    <div class="popup-header">
                        <div class="popup-icon">
                            ${item.image && item.image.startsWith('http')
                                ? `<img src="${item.image}" alt="${item.name}" class="popup-image" />`
                                : `<svg class="popup-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">${getIconPath(item.image || 'pin')}</svg>`
                            }
                        </div>
                        <div class="popup-content">
                            <h3 class="popup-title">${item.name}</h3>
                            ${item.description ? `<p class="popup-description">${item.description}</p>` : ''}
                        </div>
                    </div>
                    ${item.address ? `
                        <div class="popup-section">
                            <div class="popup-label">
                                <svg class="popup-label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25s-7.5-4.108-7.5-11.25a7.5 7.5 0 1115 0z"/>
                                </svg>
                                Address
                            </div>
                            <p class="popup-text">${item.address}</p>
                        </div>
                    ` : ''}
                    <div class="popup-section">
                        <div class="popup-label">
                            <svg class="popup-label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.158.69-.158 1.006 0l4.994 2.497c.317.158.69.158 1.006 0z"/>
                            </svg>
                            Coordinates
                        </div>
                        <p class="popup-coordinates">${parseFloat(item.latitude).toFixed(6)}, ${parseFloat(item.longitude).toFixed(6)}</p>
                    </div>
                </div>
            `;

            new maplibregl.Marker({ element: el })
                .setLngLat([item.longitude, item.latitude])
                .setPopup(new maplibregl.Popup({
                    offset: 35,
                    closeButton: true,
                    maxWidth: '320px',
                    className: 'custom-popup'
                }).setHTML(popupContent))
                .addTo(map.value);

            bounds.extend([item.longitude, item.latitude]);
        }
    });

    if (!bounds.isEmpty()) {
        map.value.fitBounds(bounds, { padding: MAP_CONFIG.fitBoundsPadding, maxZoom: 16 });
    } else {
        map.value.setCenter([mapData.value.longitude || MAP_CONFIG.center[0], mapData.value.latitude || MAP_CONFIG.center[1]]);
        map.value.setZoom(mapData.value.zoom || MAP_CONFIG.zoom);
    }
};

// Search and pagination functions
const handleSearch = async () => {
    currentPage.value = 1;
    await fetchMapData(1, searchQuery.value);
};

const handlePageChange = async (page) => {
    await fetchMapData(page, searchQuery.value);
};

const handlePerPageChange = async (newPerPage) => {
    itemsPerPage.value = newPerPage;
    currentPage.value = 1; // Reset to first page when changing per page
    await fetchMapData(1, searchQuery.value);
};

const clearSearch = async () => {
    searchQuery.value = '';
    currentPage.value = 1;
    await fetchMapData(1, '');
};

const toggleSidebar = () => {
    showSidebar.value = !showSidebar.value;
};

const selectPlace = (place) => {
    if (place.latitude && place.longitude && map.value) {
        map.value.flyTo({
            center: [place.longitude, place.latitude],
            zoom: 16,
            duration: 1000
        });

        // Hide sidebar on mobile after selection
        if (window.innerWidth < 640) {
            showSidebar.value = false;
        }
    }
};

// Custom data functions
const fetchCustomData = async (placeMapId, dataMapId, dataMapItemId, page = 1) => {
    customDataLoading.value = true;
    customDataError.value = null;

    try {
        const params = new URLSearchParams({
            page: page.toString(),
            perPage: customDataItemsPerPage.value.toString(),
            dataMapID: dataMapId,
            dataMapItemID: dataMapItemId
        });

        const response = await axios.get(`/map/data/get-custom-data?${params}`);

        customDataItems.value = response.data.data;
        customDataPagination.value = {
            currentPage: response.data.currentPage,
            lastPage: response.data.lastPage,
            total: response.data.total,
            perPage: response.data.perPage,
        };
        customDataCurrentPage.value = response.data.currentPage;
    } catch (err) {
        console.error('Error fetching custom data:', err);
        customDataError.value = 'Failed to load custom data';
    } finally {
        customDataLoading.value = false;
    }
};

const openCustomDataSidebar = async (place) => {
    selectedPlaceItem.value = place;
    showCustomDataSidebar.value = true;

    // Find the data map item for this place
    if (mapData.value && mapData.value.data_map && mapData.value.data_map.length > 0) {
        const dataMap = mapData.value.data_map[0];
        if (dataMap.data_map_items && dataMap.data_map_items.length > 0) {
            const dataMapItem = dataMap.data_map_items.find(item => item.place_map_item_id === place.id);
            if (dataMapItem) {
                await fetchCustomData(mapData.value.id, dataMap.id, dataMapItem.id);
            }
        }
    }
};

const closeCustomDataSidebar = () => {
    showCustomDataSidebar.value = false;
    selectedPlaceItem.value = null;
    customDataItems.value = [];
    customDataPagination.value = null;
    customDataError.value = null;
};

const handleCustomDataPageChange = async (page) => {
    if (selectedPlaceItem.value && mapData.value) {
        const dataMap = mapData.value.data_map[0];
        const dataMapItem = dataMap.data_map_items.find(item => item.place_map_item_id === selectedPlaceItem.value.id);
        if (dataMapItem) {
            await fetchCustomData(mapData.value.id, dataMap.id, dataMapItem.id, page);
        }
    }
};

// Helper function to get first 5 fields from custom data
const getDisplayFields = (item) => {
    if (!item || typeof item !== 'object') return [];

    const fields = Object.entries(item)
        .filter(([key]) => !['id', 'timestamp'].includes(key.toLowerCase()))
        .slice(0, 5);

    return fields;
};

// Helper function to get remaining fields (beyond first 5)
const getHiddenFields = (item) => {
    if (!item || typeof item !== 'object') return [];

    const fields = Object.entries(item)
        .filter(([key]) => !['id', 'timestamp'].includes(key.toLowerCase()))
        .slice(5);

    return fields;
};

// Helper function to check if place has custom data available
const hasCustomData = (place) => {
    if (!mapData.value || !mapData.value.data_map || mapData.value.data_map.length === 0) {
        return false;
    }

    const dataMap = mapData.value.data_map[0];
    if (!dataMap.data_map_items || dataMap.data_map_items.length === 0) {
        return false;
    }

    return dataMap.data_map_items.some(item => item.place_map_item_id === place.id);
};

// Watch for search query changes for live search
watch(searchQuery, (newQuery) => {
    debouncedSearch(newQuery);
});

const getPaginationPages = () => {
    if (!placeItems.value) return [];

    const current = currentPage.value;
    const last = placeItems.value.lastPage;
    const pages = [];

    if (last <= 7) {
        // Show all pages if 7 or fewer
        for (let i = 1; i <= last; i++) {
            pages.push(i);
        }
    } else {
        // Show smart pagination
        if (current <= 4) {
            pages.push(1, 2, 3, 4, 5, '...', last);
        } else if (current >= last - 3) {
            pages.push(1, '...', last - 4, last - 3, last - 2, last - 1, last);
        } else {
            pages.push(1, '...', current - 1, current, current + 1, '...', last);
        }
    }

    return pages;
};

const getIconPath = (name) => {
    const iconPaths = {
        pin: `<path d="M12 6.5a2.5 2.5 0 100 5 2.5 2.5 0 000-5z"/>`,
        star: `<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
        heart: `<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
        flag: `<path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
        home: `<path d="M9 22V12h6v10"/><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>`,
        work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/>`,
        cafe: `<path d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"/>`,
        park: `<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6"/><path d="M1 12h6m6 0h6"/>`,
        restaurant: `<path d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
        shopping: `<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
        hospital: `<path d="M12 6v12"/><path d="M18 12H6"/><rect x="3" y="3" width="18" height="18" rx="2"/>`,
        school: `<path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
    };
    return iconPaths[name] || iconPaths.pin;
};

const applyMapStyle = (themeName) => {
    if (!map.value || !map.value.isStyleLoaded()) return;
    const theme = UI_CONFIG.mapThemes[themeName];
    if (theme) {
        const source = map.value.getSource('osm');
        if (source) {
            source.setTiles([theme.url]);
        }

        const defaultPaint = { 'raster-saturation': 0, 'raster-contrast': 0, 'raster-opacity': 1 };
        Object.keys(defaultPaint).forEach(key => {
            map.value.setPaintProperty('osm-tiles', key, defaultPaint[key]);
        });

        const newPaint = theme.paint || {};
        Object.keys(newPaint).forEach(key => {
            map.value.setPaintProperty('osm-tiles', key, newPaint[key]);
        });

        selectedTheme.value = themeName;
        localStorage.setItem('mapTheme', themeName);
    }
};

// --- LIFECYCLE HOOKS ---
onMounted(() => {
    if (!mapContainer.value) {
        error.value = 'Map container element not found.';
        isLoading.value = false;
        return;
    }
    const initialTheme = UI_CONFIG.mapThemes[selectedTheme.value] || UI_CONFIG.mapThemes['Light'];

    try {
        map.value = new maplibregl.Map({
            container: mapContainer.value,
            style: {
                version: 8,
                sources: { osm: { type: 'raster', tiles: [initialTheme.url], tileSize: 256, attribution: initialTheme.attribution } },
                layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: initialTheme.paint || {} }],
            },
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            attributionControl: false,
        });
        map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');
        map.value.on('load', fetchMapData);
    } catch (err) {
        console.error('Map initialization error:', err);
        error.value = 'Failed to initialize the map.';
        isLoading.value = false;
    }
});

onUnmounted(() => {
    map.value?.remove();
});

</script>

<template>
    <AppLayout :title="mapData ? mapData.name : 'Shared Map'">
        <Head>
            <title>{{ mapData ? `${mapData.name} - Shared Map` : 'Shared Map' }}</title>
            <meta v-if="mapData" name="description" :content="mapData.description || `A shared map named ${mapData.name}`">
        </Head>

        <div class="relative w-screen h-screen font-sans">
            <!-- Map Container -->
            <main ref="mapContainer" class="w-full h-full"></main>

            <!-- Floating Sidebar Toggle (Mobile) -->
            <div v-if="!showSidebar" class="absolute top-4 left-4 z-20 sm:hidden pointer-events-auto">
                <Button @click="toggleSidebar">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </Button>
            </div>

            <!-- Sidebar Panel -->
            <div v-if="showSidebar" class="absolute top-0 left-0 z-10 h-full w-full sm:w-auto p-2 sm:p-4 flex flex-col gap-4 pointer-events-none">
                <!-- Map Header Panel -->
                <div class="w-full sm:w-80 md:w-96 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto">
                    <div class="p-4">
                        <div v-if="mapData && !isLoading && !error" class="flex items-center gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                                <SvgIcon :name="mapData.image || 'pin'" :size="24" class="text-gray-700" />
                            </div>
                            <div class="flex-1 min-w-0">
                                <h1 class="text-xl font-semibold text-gray-900 truncate">{{ mapData.name }}</h1>
                                <p v-if="mapData.description" class="text-sm text-gray-600 mt-0.5 line-clamp-2">{{ mapData.description }}</p>
                                <div v-if="placeItems" class="flex items-center gap-2 mt-2">
                                    <span class="text-xs text-gray-500">{{ placeItems.total }} places</span>
                                    <span class="text-xs text-gray-400">•</span>
                                    <span class="text-xs text-gray-500">Page {{ placeItems.currentPage }} of {{ placeItems.lastPage }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Loading State -->
                        <div v-if="isLoading" class="flex flex-col items-center justify-center py-8">
                            <div class="animate-spin rounded-full h-12 w-12 border-2 border-gray-300 border-t-gray-900 mb-4"></div>
                            <p class="text-gray-700 text-lg font-medium">Loading Map...</p>
                        </div>

                        <!-- Error State -->
                        <div v-if="error" class="flex flex-col items-center justify-center py-8 text-center">
                            <div class="w-16 h-16 text-gray-400 mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 mb-2">Unable to Load Map</h2>
                            <p class="text-gray-600 max-w-md">{{ error }}</p>
                        </div>
                    </div>
                </div>

                <!-- Search Panel -->
                <div v-if="!isLoading && !error" class="w-full sm:w-80 md:w-96 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto">
                    <div class="p-4 space-y-4">
                        <!-- Search Input -->
                        <div class="relative">
                            <svg class="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                            </svg>
                            <Input
                                v-model="searchQuery"
                                placeholder="Search places by name, description, or address..."
                                class="w-full pl-10 pr-10 py-2.5 text-base rounded-lg border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                            />
                            <div v-if="isSearching" class="absolute right-3 top-1/2 -translate-y-1/2">
                                <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Search Results & Per Page Selector -->
                        <div class="flex items-center justify-between">
                            <div v-if="searchQuery" class="text-xs text-gray-500">
                                {{ placeItems?.pageItems || 0 }} results found
                            </div>
                            <div v-else class="text-xs text-gray-500">
                                {{ placeItems?.total || 0 }} total places
                            </div>

                            <div class="flex items-center gap-2">
                                <!-- Per Page Selector -->
                                <div class="flex items-center gap-1">
                                    <span class="text-xs text-gray-500">Show:</span>
                                    <select
                                        v-model="itemsPerPage"
                                        @change="handlePerPageChange(itemsPerPage)"
                                        class="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option v-for="option in perPageOptions" :key="option" :value="option">
                                            {{ option }}
                                        </option>
                                    </select>
                                </div>

                                <!-- Clear Search Button -->
                                <Button v-if="searchQuery" variant="outline" size="sm" @click="clearSearch">
                                    Clear
                                </Button>
                            </div>
                        </div>

                        <!-- Quick Navigation (when multiple pages) -->
                        <div v-if="placeItems && placeItems.lastPage > 1" class="pt-3 border-t border-gray-200/50">
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
                                <span>Quick Navigation</span>
                                <span>{{ placeItems.currentPage }}/{{ placeItems.lastPage }}</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <Button
                                    @click="handlePageChange(currentPage - 1)"
                                    :disabled="currentPage <= 1 || isSearching"
                                    variant="outline"
                                    size="sm"
                                    class="flex-1 text-xs"
                                >
                                    ‹ Prev
                                </Button>
                                <Button
                                    @click="handlePageChange(currentPage + 1)"
                                    :disabled="currentPage >= placeItems.lastPage || isSearching"
                                    variant="outline"
                                    size="sm"
                                    class="flex-1 text-xs"
                                >
                                    Next ›
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Panel -->
                <div v-if="placeItems && !isLoading && !error" class="w-full sm:w-80 md:w-96 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto flex flex-col flex-1 min-h-0">
                    <div class="px-4 py-2 border-b border-gray-200/80">
                        <div class="text-sm text-gray-600">
                            <span v-if="isSearching">Searching...</span>
                            <span v-else-if="placeItems.total > 0">{{ placeItems.total }} place{{ placeItems.total !== 1 ? 's' : '' }} found</span>
                            <span v-else-if="searchQuery">No places found</span>
                            <span v-else>{{ placeItems.total }} place{{ placeItems.total !== 1 ? 's' : '' }}</span>
                        </div>
                    </div>

                    <div class="flex-1 overflow-y-auto">
                        <div v-if="!isSearching && placeItems.total === 0 && searchQuery" class="p-6 text-center text-gray-500">
                            <h3 class="font-medium text-gray-900">No places found</h3>
                            <p class="mt-1 text-sm">Try a different search term.</p>
                        </div>

                        <!-- Places List -->
                        <ul v-else-if="placeItems.data.length > 0" class="space-y-1 p-2">
                            <li v-for="place in placeItems.data" :key="place.id"
                                class="p-2.5 hover:bg-blue-50 rounded-lg transition-colors duration-150 group">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 w-3 h-3 rounded-full bg-blue-600"></div>
                                    <div class="flex-1 min-w-0 cursor-pointer" @click="selectPlace(place)">
                                        <p class="text-sm font-medium text-gray-900 truncate group-hover:text-blue-800">{{ place.name }}</p>
                                        <p v-if="place.description" class="text-xs text-gray-500 truncate">{{ place.description }}</p>
                                        <p v-if="place.address" class="text-xs text-gray-500 truncate">{{ place.address }}</p>
                                        <p v-if="place.latitude && place.longitude" class="text-xs text-gray-400 font-mono">{{ parseFloat(place.latitude).toFixed(4) }}, {{ parseFloat(place.longitude).toFixed(4) }}</p>
                                    </div>
                                    <div v-if="hasCustomData(place)" class="flex-shrink-0">
                                        <Button
                                            @click.stop="openCustomDataSidebar(place)"
                                            variant="outline"
                                            size="sm"
                                            class="text-xs px-2 py-1"
                                        >
                                            View Data
                                        </Button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <!-- Pagination -->
                    <div v-if="placeItems.lastPage > 1" class="border-t border-gray-200/80 p-4">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-xs text-gray-500">
                                Showing {{ ((placeItems.currentPage - 1) * itemsPerPage) + 1 }}-{{ Math.min(placeItems.currentPage * itemsPerPage, placeItems.total) }} of {{ placeItems.total }}
                            </span>
                            <span class="text-xs text-gray-500">
                                Page {{ placeItems.currentPage }} of {{ placeItems.lastPage }}
                            </span>
                        </div>

                        <!-- Page Navigation -->
                        <div class="flex items-center gap-1 mb-3">
                            <!-- First Page -->
                            <Button
                                @click="handlePageChange(1)"
                                :disabled="currentPage <= 1 || isSearching"
                                variant="outline"
                                size="sm"
                                class="px-2"
                            >
                                ««
                            </Button>

                            <!-- Previous Page -->
                            <Button
                                @click="handlePageChange(currentPage - 1)"
                                :disabled="currentPage <= 1 || isSearching"
                                variant="outline"
                                size="sm"
                                class="px-2"
                            >
                                ‹
                            </Button>

                            <!-- Page Numbers -->
                            <div class="flex items-center gap-1 flex-1 justify-center">
                                <template v-for="page in getPaginationPages()" :key="page">
                                    <Button
                                        v-if="page !== '...'"
                                        @click="handlePageChange(page)"
                                        :disabled="isSearching"
                                        :variant="page === currentPage ? 'default' : 'outline'"
                                        size="sm"
                                        class="w-8 h-8 p-0"
                                    >
                                        {{ page }}
                                    </Button>
                                    <span v-else class="px-1 text-gray-400 text-sm">...</span>
                                </template>
                            </div>

                            <!-- Next Page -->
                            <Button
                                @click="handlePageChange(currentPage + 1)"
                                :disabled="currentPage >= placeItems.lastPage || isSearching"
                                variant="outline"
                                size="sm"
                                class="px-2"
                            >
                                ›
                            </Button>

                            <!-- Last Page -->
                            <Button
                                @click="handlePageChange(placeItems.lastPage)"
                                :disabled="currentPage >= placeItems.lastPage || isSearching"
                                variant="outline"
                                size="sm"
                                class="px-2"
                            >
                                »»
                            </Button>
                        </div>

                        <!-- Quick Page Jump -->
                        <div class="flex items-center gap-2 text-xs">
                            <span class="text-gray-500">Go to page:</span>
                            <input
                                type="number"
                                :min="1"
                                :max="placeItems.lastPage"
                                v-model.number="currentPage"
                                @keyup.enter="handlePageChange(currentPage)"
                                @blur="handlePageChange(currentPage)"
                                class="w-16 px-2 py-1 border border-gray-300 rounded text-center focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                            <span class="text-gray-500">of {{ placeItems.lastPage }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Data Sidebar -->
            <div v-if="showCustomDataSidebar" class="absolute top-0 right-0 z-20 h-full w-full sm:w-auto p-2 sm:p-4 flex flex-col gap-4 pointer-events-none">
                <div class="w-full sm:w-80 md:w-96 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto flex flex-col h-full">
                    <!-- Header -->
                    <div class="p-4 border-b border-gray-200/80 flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Custom Data</h3>
                                <p v-if="selectedPlaceItem" class="text-sm text-gray-600 truncate">{{ selectedPlaceItem.name }}</p>
                            </div>
                        </div>
                        <Button @click="closeCustomDataSidebar" variant="outline" size="sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </Button>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 overflow-y-auto">
                        <!-- Loading State -->
                        <div v-if="customDataLoading" class="flex flex-col items-center justify-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600 mb-4"></div>
                            <p class="text-gray-600">Loading custom data...</p>
                        </div>

                        <!-- Error State -->
                        <div v-else-if="customDataError" class="p-4 text-center">
                            <div class="text-red-500 mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <p class="text-gray-600">{{ customDataError }}</p>
                        </div>

                        <!-- No Data State -->
                        <div v-else-if="!customDataItems || customDataItems.length === 0" class="p-4 text-center">
                            <div class="text-gray-400 mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <p class="text-gray-600">No custom data available</p>
                        </div>

                        <!-- Data Items -->
                        <div v-else class="p-4 space-y-4">
                            <div v-for="(item, index) in customDataItems" :key="item.id || index"
                                 class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                                <!-- Display Fields (First 5) -->
                                <div class="space-y-2">
                                    <div v-for="[key, value] in getDisplayFields(item)" :key="key"
                                         class="flex justify-between items-start">
                                        <span class="text-sm font-medium text-gray-700 capitalize">{{ key }}:</span>
                                        <span class="text-sm text-gray-900 text-right max-w-[60%] break-words">{{ value }}</span>
                                    </div>
                                </div>

                                <!-- Show More Button & Hidden Fields -->
                                <div v-if="getHiddenFields(item).length > 0" class="mt-3">
                                    <details class="group">
                                        <summary class="cursor-pointer text-sm text-blue-600 hover:text-blue-800 font-medium">
                                            Show {{ getHiddenFields(item).length }} more field{{ getHiddenFields(item).length !== 1 ? 's' : '' }}
                                        </summary>
                                        <div class="mt-2 pt-2 border-t border-gray-100 space-y-2">
                                            <div v-for="[key, value] in getHiddenFields(item)" :key="key"
                                                 class="flex justify-between items-start">
                                                <span class="text-sm font-medium text-gray-700 capitalize">{{ key }}:</span>
                                                <span class="text-sm text-gray-900 text-right max-w-[60%] break-words">{{ value }}</span>
                                            </div>
                                        </div>
                                    </details>
                                </div>

                                <!-- Timestamp -->
                                <div v-if="item.timestamp" class="mt-3 pt-2 border-t border-gray-100">
                                    <div class="flex justify-between items-center">
                                        <span class="text-xs font-medium text-gray-500">Timestamp:</span>
                                        <span class="text-xs text-gray-600 font-mono">{{ item.timestamp }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div v-if="customDataPagination && customDataPagination.lastPage > 1" class="border-t border-gray-200/80 p-4">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-xs text-gray-500">
                                {{ customDataPagination.total }} total items
                            </span>
                            <span class="text-xs text-gray-500">
                                Page {{ customDataPagination.currentPage }} of {{ customDataPagination.lastPage }}
                            </span>
                        </div>

                        <div class="flex items-center gap-1">
                            <Button
                                @click="handleCustomDataPageChange(customDataCurrentPage - 1)"
                                :disabled="customDataCurrentPage <= 1 || customDataLoading"
                                variant="outline"
                                size="sm"
                                class="flex-1 text-xs"
                            >
                                ‹ Prev
                            </Button>
                            <Button
                                @click="handleCustomDataPageChange(customDataCurrentPage + 1)"
                                :disabled="customDataCurrentPage >= customDataPagination.lastPage || customDataLoading"
                                variant="outline"
                                size="sm"
                                class="flex-1 text-xs"
                            >
                                Next ›
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>



<style>
html, body, #app {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

/* Popup Container Styles */
.maplibregl-popup-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 0;
    font-family: inherit;
    border: 1px solid rgba(0, 0, 0, 0.08);
    overflow: hidden;
    min-width: 280px;
    max-width: 320px;
}

.maplibregl-popup-close-button {
    color: #9ca3af;
    font-size: 20px;
    padding: 8px;
    margin: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
}

.maplibregl-popup-close-button:hover {
    background: #f3f4f6;
    color: #374151;
    transform: scale(1.05);
}

.maplibregl-popup-anchor-bottom .maplibregl-popup-tip {
    border-top-color: white;
}

/* Custom Popup Card Styles */
.popup-card {
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.popup-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.popup-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 2px solid white;
}

.popup-image {
    width: 36px;
    height: 36px;
    object-fit: cover;
    border-radius: 8px;
}

.popup-svg {
    width: 24px;
    height: 24px;
    color: white;
}

.popup-content {
    flex: 1;
    min-width: 0;
}

.popup-title {
    font-size: 18px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
    line-height: 1.3;
    word-wrap: break-word;
}

.popup-description {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
    word-wrap: break-word;
}

.popup-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.popup-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 8px;
}

.popup-label-icon {
    width: 14px;
    height: 14px;
    color: #9ca3af;
}

.popup-text {
    font-size: 14px;
    color: #374151;
    margin: 0;
    line-height: 1.5;
    word-wrap: break-word;
}

.popup-coordinates {
    font-size: 13px;
    color: #6b7280;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    margin: 0;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Responsive Design */
@media (max-width: 480px) {
    .maplibregl-popup-content {
        min-width: 260px;
        max-width: 280px;
    }

    .popup-card {
        padding: 16px;
    }

    .popup-header {
        gap: 12px;
        margin-bottom: 12px;
    }

    .popup-icon {
        width: 40px;
        height: 40px;
    }

    .popup-image {
        width: 28px;
        height: 28px;
    }

    .popup-svg {
        width: 20px;
        height: 20px;
    }

    .popup-title {
        font-size: 16px;
    }

    .popup-section {
        margin-top: 12px;
        padding-top: 12px;
    }
}

.custom-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.25));
}

.marker-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1f2937;
    border-radius: 50%;
    padding: 8px;
    transition: all 0.2s ease;
    border: 3px solid white;
    width: 48px;
    height: 48px;
}

.marker-custom-image {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: 50%;
}

.custom-marker:hover .marker-icon-container {
    transform: scale(1.1);
    background: #111827;
}

.custom-marker svg {
    fill: #1f2937;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}


</style>