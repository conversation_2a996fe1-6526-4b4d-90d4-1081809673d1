<?php

return [
    // datamap sub-items field for tracking, type is multi
    'gps-field' => [
        [
            'name' => 'Address',
            'length' => 50,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes' // this means user is not allowed to change this field
        ],
        [
            'name' => 'Timestamp',
            'length' => 30,
            'isRequired' => 'no',
            'type' => 'date',
            'default' => 'yes'
        ],
        [
            'name' => 'Latitude',
            'length' => null,
            'isRequired' => 'yes',
            'type' => 'string',
            'default' => 'yes'
        ],
        [
            'name' => 'Longitude',
            'length' => null,
            'isRequired' => 'yes',
            'type' => 'string',
            'default' => 'yes'
        ],
        [
            'name' => 'Details',
            'length' => 255,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ]
    ],

    // for placemapitem
    'geo-fancing' => [
        [
            'name' => 'ID',
            'type' => 'string',
            'length' => 36,
            'isRequired' => 'no',
            'type' => 'number',
            'default' => 'yes'
        ],
        [
            'name' => 'ID-Type', //  cell, sector , village
            'type' => 'string',
            'length' => 30,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Action-Trigger', // send email
            'type' => 'string',
            'length' => 100,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Type', // when tracking is away , when item is close , when item is in range
            'type' => 'string',
            'length' => 30,
            'isRequired' => 'no',
            'type' => 'text',
            'default' => 'yes'
        ],
        [
            'name' => 'Radius', // away in 10km , close range of 10km 
            'type' => 'string',
            'length' => 30,
            'isRequired' => 'no',
            'type' => 'number',
            'default' => 'yes'
        ],
    ],

];